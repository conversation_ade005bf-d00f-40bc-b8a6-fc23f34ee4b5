"""
健康检查路由 - 提供系统健康状态检查接口
"""
from fastapi import APIRouter
from pydantic import BaseModel
from typing import Dict, Any


# 创建路由器，指定前缀和标签
router = APIRouter(prefix="/api", tags=["健康检查"])


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    message: str
    timestamp: str


@router.get("/health", 
           summary="健康检查",
           description="检查系统健康状态",
           response_model=HealthResponse)
async def health_check() -> HealthResponse:
    """
    健康检查接口
    
    Returns:
        HealthResponse: 包含系统状态信息的响应
    """
    from datetime import datetime
    
    return HealthResponse(
        status="ok",
        message="系统运行正常",
        timestamp=datetime.now().isoformat()
    )


@router.get("/ping",
           summary="简单ping检查", 
           description="最简单的存活检查")
async def ping() -> Dict[str, str]:
    """
    简单的ping接口，只返回ok
    
    Returns:
        Dict[str, str]: 简单的ok响应
    """
    return {"status": "ok"}
