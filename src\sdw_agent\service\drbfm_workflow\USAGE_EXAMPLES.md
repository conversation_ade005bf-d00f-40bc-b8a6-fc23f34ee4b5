# DRBFM工作流使用示例

## API调用示例

以下是DRBFM工作流各个步骤的API调用示例：

### 1. 要件和SCL分析

```bash
curl -X POST "http://localhost:8001/api/sdw/drbfm/analysis" \
  -H "Content-Type: application/json" \
  -d '{
    "project_id": "PROJECT_001",
    "requirements_file_path": "/path/to/requirements.xlsx",
    "scl_file_path": "/path/to/scl.xlsx",
    "analysis_scope": "全系统分析"
  }'
```

### 2. 变更概要写入DRBFM

```bash
curl -X POST "http://localhost:8001/api/sdw/drbfm/summary" \
  -H "Content-Type: application/json" \
  -d '{
    "drbfm_file_path": "/path/to/drbfm.xlsx",
    "change_summary": "系统升级变更",
    "change_reason": "性能优化需求",
    "change_scope": "核心处理模块"
  }'
```

### 3. Block图生成并写入DRBFM

```bash
curl -X POST "http://localhost:8001/api/sdw/drbfm/block_diagram" \
  -H "Content-Type: application/json" \
  -d '{
    "drbfm_file_path": "/path/to/drbfm.xlsx",
    "system_components": ["CPU", "Memory", "Storage", "Network"],
    "component_relationships": {
      "CPU": ["Memory", "Storage"],
      "Memory": ["Storage"],
      "Network": ["CPU"]
    },
    "highlight_components": ["CPU", "Memory"]
  }'
```

### 4. 变更点、变化点比较作成

```bash
curl -X POST "http://localhost:8001/api/sdw/drbfm/change_point" \
  -H "Content-Type: application/json" \
  -d '{
    "drbfm_file_path": "/path/to/drbfm.xlsx",
    "before_state": {
      "cpu_frequency": "2.0GHz",
      "memory_size": "4GB",
      "storage_type": "HDD"
    },
    "after_state": {
      "cpu_frequency": "3.0GHz",
      "memory_size": "8GB",
      "storage_type": "SSD"
    },
    "change_points": ["CPU频率提升", "内存容量增加", "存储类型变更"],
    "impact_analysis": "性能提升50%，功耗增加20%"
  }'
```

### 5. 担心点抽出表作成

```bash
curl -X POST "http://localhost:8001/api/sdw/drbfm/concern" \
  -H "Content-Type: application/json" \
  -d '{
    "drbfm_file_path": "/path/to/drbfm.xlsx",
    "concern_categories": ["安全性", "可靠性", "性能", "兼容性"],
    "risk_factors": [
      {"factor": "过热风险", "category": "安全性"},
      {"factor": "数据丢失", "category": "可靠性"},
      {"factor": "响应延迟", "category": "性能"}
    ],
    "severity_levels": {
      "安全性": 4,
      "可靠性": 3,
      "性能": 2,
      "兼容性": 1
    }
  }'
```

### 6. FTA作成

```bash
curl -X POST "http://localhost:8001/api/sdw/drbfm/fta" \
  -H "Content-Type: application/json" \
  -d '{
    "drbfm_file_path": "/path/to/drbfm.xlsx",
    "failure_modes": ["系统崩溃", "数据损坏", "性能下降"],
    "root_causes": [
      {"cause": "硬件故障", "failure_mode": "系统崩溃"},
      {"cause": "软件错误", "failure_mode": "数据损坏"},
      {"cause": "资源不足", "failure_mode": "性能下降"}
    ],
    "probability_data": {
      "系统崩溃": 0.001,
      "数据损坏": 0.005,
      "性能下降": 0.02
    }
  }'
```

### 7. DRBFM sheet页作成

```bash
curl -X POST "http://localhost:8001/api/sdw/drbfm/sheet" \
  -H "Content-Type: application/json" \
  -d '{
    "drbfm_file_path": "/path/to/drbfm.xlsx",
    "project_info": {
      "project_name": "系统升级项目",
      "project_version": "v2.0",
      "project_manager": "张三"
    },
    "review_members": ["李四", "王五", "赵六"],
    "review_date": "2024-01-15"
  }'
```

## Python代码示例

### 基本使用

```python
from sdw_agent.service.drbfm_workflow import (
    DrbfmWorkflow,
    DrbfmAnalysisRequest,
    DrbfmSummaryRequest
)

# 创建工作流实例
workflow = DrbfmWorkflow()

# 步骤1: 要件和SCL分析
analysis_request = DrbfmAnalysisRequest(
    project_id="PROJECT_001",
    requirements_file_path="/path/to/requirements.xlsx",
    scl_file_path="/path/to/scl.xlsx",
    analysis_scope="全系统分析"
)

result = workflow.execute_analysis(analysis_request)
if result.status.value == "成功":
    print("分析完成:", result.data)
else:
    print("分析失败:", result.error)

# 步骤2: 变更概要写入
summary_request = DrbfmSummaryRequest(
    drbfm_file_path="/path/to/drbfm.xlsx",
    change_summary="系统升级变更",
    change_reason="性能优化需求",
    change_scope="核心处理模块"
)

result = workflow.execute_summary(summary_request)
if result.status.value == "成功":
    print("概要写入完成:", result.data)
else:
    print("概要写入失败:", result.error)
```

### 完整工作流示例

```python
from sdw_agent.service.drbfm_workflow import *

def run_complete_drbfm_workflow():
    """运行完整的DRBFM工作流"""
    workflow = DrbfmWorkflow()
    
    # 步骤1: 要件和SCL分析
    print("步骤1: 要件和SCL分析")
    analysis_request = DrbfmAnalysisRequest(
        project_id="PROJECT_001",
        requirements_file_path="/path/to/requirements.xlsx",
        scl_file_path="/path/to/scl.xlsx",
        analysis_scope="全系统分析"
    )
    result = workflow.execute_analysis(analysis_request)
    print(f"结果: {result.status.value}")
    
    # 步骤2: 变更概要写入
    print("步骤2: 变更概要写入")
    summary_request = DrbfmSummaryRequest(
        drbfm_file_path="/path/to/drbfm.xlsx",
        change_summary="系统升级变更",
        change_reason="性能优化需求",
        change_scope="核心处理模块"
    )
    result = workflow.execute_summary(summary_request)
    print(f"结果: {result.status.value}")
    
    # 步骤3: Block图生成
    print("步骤3: Block图生成")
    block_request = DrbfmBlockDiagramRequest(
        drbfm_file_path="/path/to/drbfm.xlsx",
        system_components=["CPU", "Memory", "Storage"],
        highlight_components=["CPU"]
    )
    result = workflow.execute_block_diagram(block_request)
    print(f"结果: {result.status.value}")
    
    # 步骤4: 变更点比较
    print("步骤4: 变更点比较")
    change_request = DrbfmChangePointRequest(
        drbfm_file_path="/path/to/drbfm.xlsx",
        before_state={"version": "1.0"},
        after_state={"version": "2.0"},
        change_points=["版本升级"],
        impact_analysis="性能提升"
    )
    result = workflow.execute_change_point(change_request)
    print(f"结果: {result.status.value}")
    
    # 步骤5: 担心点抽出
    print("步骤5: 担心点抽出")
    concern_request = DrbfmConcernRequest(
        drbfm_file_path="/path/to/drbfm.xlsx",
        concern_categories=["安全性", "可靠性"],
        risk_factors=[{"factor": "风险1"}],
        severity_levels={"安全性": 3}
    )
    result = workflow.execute_concern(concern_request)
    print(f"结果: {result.status.value}")
    
    # 步骤6: FTA作成
    print("步骤6: FTA作成")
    fta_request = DrbfmFtaRequest(
        drbfm_file_path="/path/to/drbfm.xlsx",
        failure_modes=["故障模式1"],
        root_causes=[{"cause": "原因1"}],
        probability_data={"故障模式1": 0.01}
    )
    result = workflow.execute_fta(fta_request)
    print(f"结果: {result.status.value}")
    
    # 步骤7: DRBFM sheet页作成
    print("步骤7: DRBFM sheet页作成")
    sheet_request = DrbfmSheetRequest(
        drbfm_file_path="/path/to/drbfm.xlsx",
        project_info={"name": "项目1"},
        review_members=["成员1"],
        review_date="2024-01-01"
    )
    result = workflow.execute_sheet(sheet_request)
    print(f"结果: {result.status.value}")
    
    print("DRBFM工作流完成!")

if __name__ == "__main__":
    run_complete_drbfm_workflow()
```

## 响应格式

所有API端点都返回统一的响应格式：

```json
{
  "code": 0,
  "msg": "成功消息",
  "data": {
    // 具体的返回数据
  }
}
```

成功时 `code` 为 0，失败时为非0值。`data` 字段包含具体的返回数据，格式根据不同的端点而异。
