"""
DRBFM工作流测试

测试DRBFM工作流的各个步骤是否能正常执行
"""

import pytest
from sdw_agent.service.drbfm_workflow import (
    DrbfmWorkflow,
    DrbfmAnalysisRequest,
    DrbfmSummaryRequest,
    DrbfmBlockDiagramRequest,
    DrbfmChangePointRequest,
    DrbfmConcernRequest,
    DrbfmFtaRequest,
    DrbfmSheetRequest
)
from sdw_agent.service import WorkflowStatus


class TestDrbfmWorkflow:
    """DRBFM工作流测试类"""

    def setup_method(self):
        """测试前准备"""
        self.workflow = DrbfmWorkflow()

    def test_workflow_initialization(self):
        """测试工作流初始化"""
        assert self.workflow is not None
        assert hasattr(self.workflow, 'execute_analysis')
        assert hasattr(self.workflow, 'execute_summary')
        assert hasattr(self.workflow, 'execute_block_diagram')
        assert hasattr(self.workflow, 'execute_change_point')
        assert hasattr(self.workflow, 'execute_concern')
        assert hasattr(self.workflow, 'execute_fta')
        assert hasattr(self.workflow, 'execute_sheet')

    def test_execute_analysis(self):
        """测试要件和SCL分析"""
        request = DrbfmAnalysisRequest(
            project_id="TEST_PROJECT_001",
            requirements_file_path="/test/requirements.xlsx",
            scl_file_path="/test/scl.xlsx",
            analysis_scope="测试分析范围"
        )
        
        result = self.workflow.execute_analysis(request)
        
        assert result.status == WorkflowStatus.SUCCESS
        assert result.data is not None
        assert "analysis_result" in result.data
        assert "requirements_summary" in result.data
        assert "scl_summary" in result.data

    def test_execute_summary(self):
        """测试变更概要写入"""
        request = DrbfmSummaryRequest(
            drbfm_file_path="/test/drbfm.xlsx",
            change_summary="测试变更概要",
            change_reason="测试变更理由",
            change_scope="测试变更范围"
        )
        
        result = self.workflow.execute_summary(request)
        
        assert result.status == WorkflowStatus.SUCCESS
        assert result.data is not None
        assert "drbfm_file_path" in result.data
        assert "summary_section" in result.data

    def test_execute_block_diagram(self):
        """测试Block图生成"""
        request = DrbfmBlockDiagramRequest(
            drbfm_file_path="/test/drbfm.xlsx",
            system_components=["Component1", "Component2", "Component3"],
            component_relationships={"Component1": ["Component2"]},
            highlight_components=["Component1"]
        )
        
        result = self.workflow.execute_block_diagram(request)
        
        assert result.status == WorkflowStatus.SUCCESS
        assert result.data is not None
        assert "block_diagram_path" in result.data
        assert "highlighted_components" in result.data

    def test_execute_change_point(self):
        """测试变更点、变化点比较"""
        request = DrbfmChangePointRequest(
            drbfm_file_path="/test/drbfm.xlsx",
            before_state={"state": "before"},
            after_state={"state": "after"},
            change_points=["变更点1", "变更点2"],
            impact_analysis="测试影响分析"
        )
        
        result = self.workflow.execute_change_point(request)
        
        assert result.status == WorkflowStatus.SUCCESS
        assert result.data is not None
        assert "comparison_table" in result.data
        assert "change_impact" in result.data

    def test_execute_concern(self):
        """测试担心点抽出表作成"""
        request = DrbfmConcernRequest(
            drbfm_file_path="/test/drbfm.xlsx",
            concern_categories=["安全性", "可靠性"],
            risk_factors=[{"factor": "风险因子1"}],
            severity_levels={"安全性": 3, "可靠性": 2}
        )
        
        result = self.workflow.execute_concern(request)
        
        assert result.status == WorkflowStatus.SUCCESS
        assert result.data is not None
        assert "concern_table" in result.data
        assert "risk_assessment" in result.data

    def test_execute_fta(self):
        """测试FTA作成"""
        request = DrbfmFtaRequest(
            drbfm_file_path="/test/drbfm.xlsx",
            failure_modes=["故障模式1", "故障模式2"],
            root_causes=[{"cause": "根本原因1"}],
            probability_data={"故障模式1": 0.01}
        )
        
        result = self.workflow.execute_fta(request)
        
        assert result.status == WorkflowStatus.SUCCESS
        assert result.data is not None
        assert "fta_diagram_path" in result.data
        assert "failure_analysis" in result.data

    def test_execute_sheet(self):
        """测试DRBFM sheet页作成"""
        request = DrbfmSheetRequest(
            drbfm_file_path="/test/drbfm.xlsx",
            project_info={"project_name": "测试项目"},
            review_members=["成员1", "成员2"],
            review_date="2024-01-01"
        )
        
        result = self.workflow.execute_sheet(request)
        
        assert result.status == WorkflowStatus.SUCCESS
        assert result.data is not None
        assert "sheet_sections" in result.data
        assert "completion_status" in result.data

    def test_execute_with_invalid_operation(self):
        """测试无效操作类型"""
        result = self.workflow.execute("invalid_operation", {})
        
        assert result.status == WorkflowStatus.FAILED
        assert "不支持的操作类型" in result.message

    def test_request_validation(self):
        """测试请求参数验证"""
        # 测试空项目ID
        with pytest.raises(ValueError):
            DrbfmAnalysisRequest(
                project_id="",
                requirements_file_path="/test/requirements.xlsx",
                scl_file_path="/test/scl.xlsx",
                analysis_scope="测试分析范围"
            )
        
        # 测试空文件路径
        with pytest.raises(ValueError):
            DrbfmSummaryRequest(
                drbfm_file_path="",
                change_summary="测试变更概要",
                change_reason="测试变更理由",
                change_scope="测试变更范围"
            )
        
        # 测试空组件列表
        with pytest.raises(ValueError):
            DrbfmBlockDiagramRequest(
                drbfm_file_path="/test/drbfm.xlsx",
                system_components=[],
                component_relationships={},
                highlight_components=[]
            )


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
