"""
DRBFM工作流数据模型

DRBFM (Design Review Based on Failure Mode) 工作流数据结构定义

定义工作流中使用的输入输出数据结构和配置模型。
"""

from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field

from sdw_agent.model.request_model import SourceInfo


class DrbfmAnalysisRequest(BaseModel):
    """要件和SCL分析请求模型"""
    RequirementSource: SourceInfo = Field(..., description="要件一览表的路径")
    SclTemplateSource: SourceInfo = Field(..., description="SCL模板文件路径")


class DrbfmSummaryRequest(BaseModel):
    """变更概要写入DRBFM请求模型"""
    drbfm_file_path: str = Field(..., description="DRBFM文件路径")
    change_summary: str = Field(..., description="变更概要")
    change_reason: str = Field(..., description="变更理由")
    change_scope: str = Field(..., description="变更范围")


class DrbfmBlockDiagramRequest(BaseModel):
    """Block图生成并写入DRBFM请求模型"""
    drbfm_file_path: str = Field(..., description="DRBFM文件路径")
    system_components: List[str] = Field(..., description="系统组件列表")
    component_relationships: Dict[str, List[str]] = Field(default_factory=dict, description="组件关系映射")
    highlight_components: List[str] = Field(default_factory=list, description="需要高亮的组件")


class DrbfmChangePointRequest(BaseModel):
    """变更点、变化点比较作成请求模型"""
    drbfm_file_path: str = Field(..., description="DRBFM文件路径")
    before_state: Dict[str, Any] = Field(..., description="变更前状态")
    after_state: Dict[str, Any] = Field(..., description="变更后状态")
    change_points: List[str] = Field(..., description="变更点列表")
    impact_analysis: str = Field(..., description="影响分析")


class DrbfmConcernRequest(BaseModel):
    """担心点抽出表作成请求模型"""
    drbfm_file_path: str = Field(..., description="DRBFM文件路径")
    concern_categories: List[str] = Field(..., description="担心点分类")
    risk_factors: List[Dict[str, Any]] = Field(..., description="风险因子列表")
    severity_levels: Dict[str, int] = Field(..., description="严重程度等级")


class DrbfmFtaRequest(BaseModel):
    """FTA作成请求模型"""
    drbfm_file_path: str = Field(..., description="DRBFM文件路径")
    failure_modes: List[str] = Field(..., description="故障模式列表")
    root_causes: List[Dict[str, Any]] = Field(..., description="根本原因列表")
    probability_data: Dict[str, float] = Field(default_factory=dict, description="概率数据")


class DrbfmSheetRequest(BaseModel):
    """DRBFM sheet页作成请求模型"""
    drbfm_file_path: str = Field(..., description="DRBFM文件路径")
    project_info: Dict[str, str] = Field(..., description="项目信息")
    review_members: List[str] = Field(..., description="评审成员列表")
    review_date: str = Field(..., description="评审日期")


# 输出数据模型
class DrbfmAnalysisOutputData(BaseModel):
    """要件和SCL分析输出数据模型"""
    analysis_result: Dict[str, Any] = Field(..., description="分析结果")
    requirements_summary: str = Field(..., description="要件摘要")
    scl_summary: str = Field(..., description="SCL摘要")
    analysis_file_path: str = Field(..., description="分析结果文件路径")


class DrbfmSummaryOutputData(BaseModel):
    """变更概要写入输出数据模型"""
    drbfm_file_path: str = Field(..., description="DRBFM文件路径")
    summary_section: str = Field(..., description="概要部分")
    write_status: str = Field(..., description="写入状态")


class DrbfmBlockDiagramOutputData(BaseModel):
    """Block图生成输出数据模型"""
    drbfm_file_path: str = Field(..., description="DRBFM文件路径")
    block_diagram_path: str = Field(..., description="Block图文件路径")
    highlighted_components: List[str] = Field(..., description="已高亮的组件")


class DrbfmChangePointOutputData(BaseModel):
    """变更点、变化点比较输出数据模型"""
    drbfm_file_path: str = Field(..., description="DRBFM文件路径")
    comparison_table: Dict[str, Any] = Field(..., description="比较表")
    change_impact: str = Field(..., description="变更影响")


class DrbfmConcernOutputData(BaseModel):
    """担心点抽出表输出数据模型"""
    drbfm_file_path: str = Field(..., description="DRBFM文件路径")
    concern_table: List[Dict[str, Any]] = Field(..., description="担心点表")
    risk_assessment: str = Field(..., description="风险评估")


class DrbfmFtaOutputData(BaseModel):
    """FTA作成输出数据模型"""
    drbfm_file_path: str = Field(..., description="DRBFM文件路径")
    fta_diagram_path: str = Field(..., description="FTA图文件路径")
    failure_analysis: Dict[str, Any] = Field(..., description="故障分析")


class DrbfmSheetOutputData(BaseModel):
    """DRBFM sheet页作成输出数据模型"""
    drbfm_file_path: str = Field(..., description="DRBFM文件路径")
    sheet_sections: List[str] = Field(..., description="sheet页部分")
    completion_status: str = Field(..., description="完成状态")


class DrbfmWorkflowConfigModel(BaseModel):
    """DRBFM工作流配置模型"""
    name: str = Field(default="DRBFM工作流", description="工作流名称")
    description: str = Field(default="基于故障模式的设计评审工作流", description="工作流描述")
    version: str = Field(default="1.0.0", description="版本号")
    author: str = Field(default="SDW Agent", description="作者")

    # 模块特定配置
    timeout: int = Field(default=600, description="超时时间（秒）")
    max_retry_count: int = Field(default=3, description="最大重试次数")
    output_format: str = Field(default="excel", description="输出格式")
